I have downloaded google-services.json into android/app/ of my Flutter project and integrated firebase_core. I want to add Firebase support to my spending tracker app (currently uses Drift SQLite for wallets, transactions, categories).

The requirements:

Data belongs to a signed-in user (via Firebase Auth UID).

User can toggle cloud sync ON/OFF. This preference should be stored locally (SQLite) and optionally in Firestore for consistency.

App is offline-first: local DB is always the source of truth. Sync happens opportunistically when the internet is available.

Sync must be two-way: push local changes to Firestore, pull down new Firestore data into SQLite.

Conflict resolution: latest update wins (timestamp-based).

Phases

Phase 1: Firebase Auth

Add firebase_auth.

Implement simple email/password login flow.

Get current user UID.

Phase 2: Firestore setup

Add cloud_firestore.

Firestore structure (scoped by UID):

users/{uid}/wallets/{walletId}
users/{uid}/transactions/{transactionId}
users/{uid}/categories/{categoryId}
settings/syncEnabled (boolean)

Phase 3: Sync toggle

Add a boolean syncEnabled setting.

Store locally in SQLite (always available offline).

If syncing is ON, also store/update in Firestore (optional).

Phase 4: One-way sync (upload only)

On login, if syncEnabled = true:

Upload local SQLite data to Firestore.

Avoid overwriting unless local item has a newer timestamp.

Phase 5: Two-way sync (download + merge)

If local DB is empty, fetch Firestore data.

Otherwise, merge:

Local newer timestamp → overwrite Firestore.

Firestore newer timestamp → overwrite local SQLite.

Phase 6: Ongoing sync

When user adds/updates/deletes locally, queue for Firestore sync (if enabled + online).

When Firestore updates are detected (listener), update local SQLite.

Ensure it gracefully handles offline → online transitions.
