---
type: "always_apply"
---

for flutter apps

- use bloc
- use clean architecture
- prioritize modularity i.e to avoid long files by modularizing whenever necessary
- sample folder structure is: lib > 1. features:(example) a) auth -> i)data (datasources,models, repositories), ii)domain (entities, repositories, usecases), iii)presentation (bloc, pages, widgets) -- this is the best structure for ease of maintenance. 2. main.dart 3. welcome_screen.dart 4. dependency_injector.dart
- in the assets folder > fonts folder, icons folder, images folder
