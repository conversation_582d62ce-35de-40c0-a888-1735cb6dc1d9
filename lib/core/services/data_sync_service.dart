import 'dart:async';
import 'dart:developer' as developer;

import '../database/repositories/wallet_repository.dart';
import '../database/repositories/transaction_repository.dart';
import '../database/repositories/category_repository.dart';
import '../database/database_helper.dart' as db;
import 'firestore_service.dart';
import 'sync_settings_service.dart';
import 'sync_status_service.dart';
import '../../dependency_injector.dart';

/// Service for synchronizing data between local SQLite and Firestore
class DataSyncService {
  final WalletRepository _walletRepository;
  final TransactionRepository _transactionRepository;
  final CategoryRepository _categoryRepository;

  DataSyncService({
    required WalletRepository walletRepository,
    required TransactionRepository transactionRepository,
    required CategoryRepository categoryRepository,
  }) : _walletRepository = walletRepository,
       _transactionRepository = transactionRepository,
       _categoryRepository = categoryRepository;

  /// Perform initial sync on login
  Future<void> performInitialSync() async {
    if (!await SyncSettingsService.canSync()) {
      developer.log(
        '⏭️ Skipping initial sync: sync not enabled or user not authenticated',
      );
      return;
    }

    try {
      developer.log('🔄 Starting initial sync on login');

      // Always prioritize cloud data on login - overwrite local with cloud data
      developer.log('📥 Overwriting local data with cloud data on login');
      await _overwriteLocalWithCloudData();

      developer.log('✅ Initial sync completed successfully');
    } catch (e) {
      developer.log('❌ Error during initial sync: $e');
      rethrow;
    }
  }

  /// Upload all local data to Firestore with enriched context
  Future<void> _uploadAllToFirestore() async {
    try {
      final wallets = await _walletRepository.getAllWallets();
      final transactions = await _transactionRepository.getAllTransactions();
      final categories = await _categoryRepository.getAllCategories();

      // Upload wallets and categories first
      await Future.wait([
        ...wallets.map((w) => FirestoreService.uploadWallet(w)),
        ...categories.map((c) => FirestoreService.uploadCategory(c)),
      ]);

      // Upload transactions with enriched context for better cross-device mapping
      for (final transaction in transactions) {
        String? walletName;
        String? walletSenderName;
        String? categoryName;
        String? categoryItemName;

        // Get wallet context
        final wallet = await _walletRepository.getWalletById(
          transaction.walletId,
        );
        walletName = wallet?.name;
        walletSenderName = wallet?.transactionSenderName;

        // Get category context if available
        if (transaction.categoryItemId != null) {
          final categoryItem = await _categoryRepository.getCategoryItemById(
            transaction.categoryItemId!,
          );
          categoryItemName = categoryItem?.name;
          if (categoryItem != null) {
            final category = await _categoryRepository.getCategoryById(
              categoryItem.categoryId,
            );
            categoryName = category?.name;
          }
        }

        await FirestoreService.uploadTransaction(
          transaction,
          walletName: walletName,
          walletSenderName: walletSenderName,
          categoryName: categoryName,
          categoryItemName: categoryItemName,
        );
      }

      developer.log('✅ All local data uploaded to Firestore');
    } catch (e) {
      developer.log('❌ Error uploading to Firestore: $e');
      rethrow;
    }
  }

  /// Overwrite local data with cloud data (for login)
  Future<void> _overwriteLocalWithCloudData() async {
    try {
      developer.log(
        '🗑️ Clearing local database before overwriting with cloud data',
      );

      // Clear all local data first
      await _clearLocalDatabase();

      // Download and populate with cloud data
      developer.log('📥 Downloading data from cloud to overwrite local');
      await _downloadAllFromFirestore();

      // Ensure default wallets exist after sync
      await _ensureDefaultWalletsExist();

      developer.log('✅ Local data overwritten with cloud data successfully');
    } catch (e) {
      developer.log('❌ Error overwriting local data with cloud data: $e');
      rethrow;
    }
  }

  /// Ensure the 6 default wallets exist after login
  Future<void> _ensureDefaultWalletsExist() async {
    try {
      developer.log('🏦 Ensuring default wallets exist...');

      final defaultWallets = [
        {'name': 'M-Pesa', 'transaction_sender_name': 'MPESA', 'amount': 0.0},
        {
          'name': 'Pochi La Biashara',
          'transaction_sender_name': 'MPESA',
          'amount': 0.0,
        },
        {'name': 'M-Shwari', 'transaction_sender_name': 'MPESA', 'amount': 0.0},
        {'name': 'SC BANK', 'transaction_sender_name': 'MPESA', 'amount': 0.0},
        {
          'name': 'EQUITY BANK',
          'transaction_sender_name': 'MPESA',
          'amount': 0.0,
        },
        {'name': 'Cash', 'transaction_sender_name': 'CASH', 'amount': 0.0},
      ];

      final existingWallets = await _walletRepository.getAllWallets();
      final existingWalletNames = existingWallets.map((w) => w.name).toSet();

      for (final walletData in defaultWallets) {
        final name = walletData['name']! as String;
        if (!existingWalletNames.contains(name)) {
          await _walletRepository.createWallet(
            name: name,
            transactionSenderName:
                walletData['transaction_sender_name']! as String,
            amount: walletData['amount']! as double,
          );
          developer.log('🏦 Created default wallet: $name');
        }
      }

      developer.log('✅ Default wallets ensured');
    } catch (e) {
      developer.log('❌ Error ensuring default wallets: $e');
      rethrow;
    }
  }

  /// Clear all local database data
  Future<void> _clearLocalDatabase() async {
    try {
      developer.log('🗑️ Clearing local database');

      // Use repositories to clear data (they handle the database access)
      await _transactionRepository.deleteAllTransactions();
      await _categoryRepository.deleteAllCategoryItems();
      await _categoryRepository.deleteAllCategories();
      await _walletRepository.deleteAllWallets();

      developer.log('✅ Local database cleared successfully');
    } catch (e) {
      developer.log('❌ Error clearing local database: $e');
      rethrow;
    }
  }

  /// Intelligent sync that compares updatedAt fields
  Future<void> _performIntelligentSync() async {
    try {
      developer.log('🔄 Starting intelligent sync with updatedAt comparison');

      final cloudData = await FirestoreService.downloadAllData();
      final cloudWallets = cloudData['wallets'] ?? [];
      final cloudCategories = cloudData['categories'] ?? [];
      final cloudTransactions = cloudData['transactions'] ?? [];

      // Sync wallets
      await _syncWallets(cloudWallets);

      // Sync categories
      await _syncCategories(cloudCategories);

      // Sync transactions
      await _syncTransactions(cloudTransactions);

      developer.log('✅ Intelligent sync completed successfully');
    } catch (e) {
      developer.log('❌ Error during intelligent sync: $e');
      rethrow;
    }
  }

  /// Sync wallets using updatedAt comparison
  Future<void> _syncWallets(List<dynamic> cloudWallets) async {
    final localWallets = await _walletRepository.getAllWallets();
    final localWalletMap = <String, Wallet>{
      for (final w in localWallets) w.name: w,
    };

    for (final cloudWalletData in cloudWallets) {
      final name = (cloudWalletData['name'] ?? '').toString();
      if (name.isEmpty) continue;

      final cloudUpdatedAt = DateTime.tryParse(
        cloudWalletData['updatedAt'] ?? '',
      );
      final localWallet = localWalletMap[name];

      if (localWallet == null) {
        // New wallet from cloud - create locally
        await _walletRepository.createWallet(
          name: name,
          transactionSenderName:
              (cloudWalletData['transactionSenderName'] ?? 'MPESA').toString(),
          amount: (cloudWalletData['balance'] is num)
              ? (cloudWalletData['balance'] as num).toDouble()
              : 0.0,
        );
        developer.log('📥 Created new wallet from cloud: $name');
      } else if (cloudUpdatedAt != null &&
          cloudUpdatedAt.isAfter(localWallet.updatedAt)) {
        // Cloud version is newer - update local
        final updatedWallet = localWallet.copyWith(
          transactionSenderName:
              (cloudWalletData['transactionSenderName'] ??
                      localWallet.transactionSenderName)
                  .toString(),
          amount: (cloudWalletData['balance'] is num)
              ? (cloudWalletData['balance'] as num).toDouble()
              : localWallet.amount,
          updatedAt: cloudUpdatedAt,
        );
        await _walletRepository.updateWallet(updatedWallet);
        developer.log('📥 Updated local wallet from cloud: $name');
      } else if (cloudUpdatedAt != null &&
          localWallet.updatedAt.isAfter(cloudUpdatedAt)) {
        // Local version is newer - sync to cloud
        await FirestoreService.syncWalletToCloud(localWallet);
        developer.log('📤 Synced local wallet to cloud: $name');
      }
    }

    // Check for local wallets not in cloud
    for (final localWallet in localWallets) {
      final existsInCloud = cloudWallets.any(
        (cw) => (cw['name'] ?? '').toString() == localWallet.name,
      );
      if (!existsInCloud) {
        // New local wallet - sync to cloud
        await FirestoreService.syncWalletToCloud(localWallet);
        developer.log(
          '📤 Synced new local wallet to cloud: ${localWallet.name}',
        );
      }
    }
  }

  /// Download all data from Firestore and merge with local database
  Future<void> _downloadAllFromFirestore() async {
    try {
      developer.log('📥 Downloading and merging data from Firestore');

      final cloudData = await FirestoreService.downloadAllData();
      final wallets = cloudData['wallets'] ?? [];
      final categories = cloudData['categories'] ?? [];
      final transactions = cloudData['transactions'] ?? [];

      // Build local lookup maps for efficient merging
      final existingWallets = await _walletRepository.getAllWallets();
      final Map<String, int> senderToWalletId = {
        for (final w in existingWallets) w.transactionSenderName: w.id,
      };
      final Map<String, int> nameToWalletId = {
        for (final w in existingWallets) w.name: w.id,
      };

      // Merge wallets by name/senderName
      for (final walletData in wallets) {
        final name = (walletData['name'] ?? '').toString();
        final sender = (walletData['transactionSenderName'] ?? '').toString();

        if (name.isEmpty) continue;

        int? existingId = senderToWalletId[sender] ?? nameToWalletId[name];
        if (existingId == null) {
          // Create new wallet
          final newId = await _walletRepository.createWallet(
            name: name,
            transactionSenderName: sender.isNotEmpty ? sender : 'MPESA',
            amount: (walletData['balance'] is num)
                ? (walletData['balance'] as num).toDouble()
                : 0.0,
          );
          senderToWalletId[sender.isNotEmpty ? sender : 'MPESA'] = newId;
          nameToWalletId[name] = newId;
        }
      }

      // Merge categories by name
      final existingCategories = await _categoryRepository.getAllCategories();
      final Map<String, int> categoryNameToId = {
        for (final c in existingCategories) c.name: c.id,
      };

      for (final categoryData in categories) {
        final name = (categoryData['name'] ?? '').toString();
        if (name.isEmpty) continue;

        if (!categoryNameToId.containsKey(name)) {
          final newId = await _categoryRepository.createCategory(name);
          categoryNameToId[name] = newId;
        }
      }

      // Merge transactions by smsHash (avoid duplicates)
      for (final transactionData in transactions) {
        final smsHash = (transactionData['smsHash'] as String?)?.trim();

        // Skip if already exists locally
        if (smsHash != null && smsHash.isNotEmpty) {
          final existing = await _transactionRepository.getTransactionBySmsHash(
            smsHash,
          );
          if (existing != null) continue;
        }

        // Map wallet by name/sender
        final walletName = (transactionData['walletName'] ?? '').toString();
        final walletSender = (transactionData['walletSenderName'] ?? '')
            .toString();
        int? walletId =
            senderToWalletId[walletSender] ?? nameToWalletId[walletName];

        if (walletId == null) {
          // Create missing wallet
          walletId = await _walletRepository.createWallet(
            name: walletName.isNotEmpty ? walletName : 'M-Pesa',
            transactionSenderName: walletSender.isNotEmpty
                ? walletSender
                : 'MPESA',
          );
          senderToWalletId[walletSender.isNotEmpty ? walletSender : 'MPESA'] =
              walletId;
          nameToWalletId[walletName.isNotEmpty ? walletName : 'M-Pesa'] =
              walletId;
        }

        // Map category if available
        int? categoryItemId;
        final categoryName = (transactionData['categoryName'] ?? '').toString();
        final categoryItemName = (transactionData['categoryItemName'] ?? '')
            .toString();

        if (categoryName.isNotEmpty && categoryItemName.isNotEmpty) {
          int? catId = categoryNameToId[categoryName];
          if (catId == null) {
            catId = await _categoryRepository.createCategory(categoryName);
            categoryNameToId[categoryName] = catId;
          }

          final items = await _categoryRepository.getCategoryItemsByCategoryId(
            catId,
          );
          final existingItem = items
              .where((i) => i.name == categoryItemName)
              .toList();

          if (existingItem.isEmpty) {
            categoryItemId = await _categoryRepository.createCategoryItem(
              name: categoryItemName,
              categoryId: catId,
            );
          } else {
            categoryItemId = existingItem.first.id;
          }
        }

        // Create transaction
        final amount = (transactionData['amount'] is num)
            ? (transactionData['amount'] as num).toDouble()
            : 0.0;
        final type = (transactionData['type'] ?? 'DEBIT').toString();
        final description = transactionData['description'] as String?;
        final date =
            DateTime.tryParse((transactionData['date'] ?? '').toString()) ??
            DateTime.now();

        await _transactionRepository.createTransaction(
          walletId: walletId,
          categoryItemId: categoryItemId,
          amount: amount,
          type: type,
          description: description,
          date: date,
          smsHash: smsHash,
          status: (transactionData['status'] ?? 'UNCATEGORIZED').toString(),
        );
      }

      developer.log('✅ Download and merge completed');
    } catch (e) {
      developer.log('❌ Error downloading from Firestore: $e');
      rethrow;
    }
  }

  /// Sync a single item to cloud (for ongoing sync)
  static Future<void> syncItemToCloud({
    db.Wallet? wallet,
    db.Transaction? transaction,
    db.Category? category,
  }) async {
    if (!await SyncSettingsService.canSync()) return;

    try {
      if (wallet != null) {
        await FirestoreService.uploadWallet(wallet);
        developer.log('📤 Synced wallet to cloud: ${wallet.name}');
      }

      if (transaction != null) {
        // Enrich transaction with context for better cross-device mapping
        String? walletName;
        String? walletSenderName;
        String? categoryName;
        String? categoryItemName;

        try {
          final walletRepo = sl<WalletRepository>();
          final categoryRepo = sl<CategoryRepository>();

          final wallet = await walletRepo.getWalletById(transaction.walletId);
          walletName = wallet?.name;
          walletSenderName = wallet?.transactionSenderName;

          if (transaction.categoryItemId != null) {
            final categoryItem = await categoryRepo.getCategoryItemById(
              transaction.categoryItemId!,
            );
            categoryItemName = categoryItem?.name;
            if (categoryItem != null) {
              final category = await categoryRepo.getCategoryById(
                categoryItem.categoryId,
              );
              categoryName = category?.name;
            }
          }
        } catch (_) {
          // Best-effort enrichment; ignore failures
        }

        await FirestoreService.uploadTransaction(
          transaction,
          walletName: walletName,
          walletSenderName: walletSenderName,
          categoryName: categoryName,
          categoryItemName: categoryItemName,
        );
        developer.log(
          '📤 Synced transaction to cloud: ${transaction.description}',
        );
      }

      if (category != null) {
        await FirestoreService.uploadCategory(category);
        developer.log('📤 Synced category to cloud: ${category.name}');
      }
    } catch (e) {
      developer.log('❌ Error syncing item to cloud: $e');
      // Don't rethrow - sync failures shouldn't break the app
    }
  }

  /// Sync item deletion to cloud
  static Future<void> syncItemDeletionToCloud({
    String? walletId,
    String? transactionId,
    String? categoryId,
  }) async {
    if (!await SyncSettingsService.canSync()) return;

    try {
      SyncStatusService.setSyncing();

      if (walletId != null) {
        await FirestoreService.deleteWallet(walletId);
        developer.log('📤 Synced wallet deletion to cloud: $walletId');
      }

      if (transactionId != null) {
        await FirestoreService.deleteTransaction(transactionId);
        developer.log(
          '📤 Synced transaction deletion to cloud: $transactionId',
        );
      }

      if (categoryId != null) {
        await FirestoreService.deleteCategory(categoryId);
        developer.log('📤 Synced category deletion to cloud: $categoryId');
      }

      SyncStatusService.setSyncCompleted();
    } catch (e) {
      developer.log('❌ Error syncing deletion to cloud: $e');
      SyncStatusService.setSyncError(e.toString());
      // Don't rethrow - sync failures shouldn't break the app
    }
  }

  /// Force full sync (upload all local data)
  static Future<void> forceFullSync() async {
    if (!await SyncSettingsService.canSync()) {
      throw Exception('Sync is not enabled or user not authenticated');
    }

    try {
      SyncStatusService.setSyncing();
      developer.log('🔄 Starting force full sync');

      final service = DataSyncService(
        walletRepository: sl<WalletRepository>(),
        transactionRepository: sl<TransactionRepository>(),
        categoryRepository: sl<CategoryRepository>(),
      );
      await service._uploadAllToFirestore();

      SyncStatusService.setSyncCompleted();
      developer.log('✅ Force full sync completed');
    } catch (e) {
      developer.log('❌ Error during force full sync: $e');
      SyncStatusService.setSyncError(e.toString());
      rethrow;
    }
  }

  /// Sync when connectivity is restored
  static Future<void> syncOnConnectivityRestored() async {
    if (!await SyncSettingsService.canSync()) return;
    if (!SyncStatusService.isConnected) return;

    try {
      developer.log('📶 Connectivity restored - starting sync');
      await forceFullSync();
    } catch (e) {
      developer.log('❌ Error syncing on connectivity restore: $e');
    }
  }

  /// Initialize connectivity-aware sync
  static void initializeConnectivitySync() {
    // Listen to connectivity changes and sync when restored
    SyncStatusService.connectivityStream.listen((isConnected) {
      if (isConnected) {
        // Delay sync to allow network to stabilize
        Timer(const Duration(seconds: 2), () {
          syncOnConnectivityRestored();
        });
      }
    });
  }
}
