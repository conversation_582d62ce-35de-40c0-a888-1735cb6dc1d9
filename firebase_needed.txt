Google Sign-In fix (ApiException: 10) — Firebase configuration needed

Android app info detected from your project:
- Package name (applicationId): com.example.spending_app
- Debug keystore SHA‑1 (from this machine): 47:44:91:A2:41:C7:3D:FC:78:47:6D:88:AD:49:2F:F4:9C:72:5B:E8

What you need to do in Firebase Console:
1) Add fingerprints for the Android app
   - Go to: Firebase Console → Project settings → Your apps → Android (com.example.spending_app)
   - Under “SHA certificate fingerprints”, click “Add fingerprint” and paste the SHA‑1 above
   - (Recommended) Also add SHA‑256 for the same keystore if you have it
   - Click Save

2) Enable Google as a Sign‑in provider
   - Firebase Console → Authentication → Sign-in method → Google → Enable
   - Set a Support email and Save

3) Download updated google-services.json
   - Back in Project settings → Your apps → Android → Download the updated google-services.json
   - Replace the file at: android/app/google-services.json

4) Clean install on device
   - Uninstall the app from the device (to clear cached config)
   - Run: flutter clean; then flutter run

Notes for Release builds (if/when you ship a release):
- You must also add the Release keystore SHA‑1 (and SHA‑256) to Firebase
- If using Play App Signing, use the SHA‑1 from Play Console → App integrity → App signing certificate

Why this fixes error 10:
- ApiException 10 (DEVELOPER_ERROR) indicates the Android OAuth client for your app isn’t provisioned.
- Adding SHA‑1 (and SHA‑256) creates the Android OAuth client in your Firebase project and updates google-services.json accordingly.

Optional: How to get your own SHA‑1 locally (if needed):
- Debug: keytool -list -v -alias androiddebugkey -keystore ~/.android/debug.keystore -storepass android -keypass android
- Release: keytool -list -v -alias <your_alias> -keystore <path_to_your_release_keystore>

